/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /**
     * gender
     *
     * - 0: "unknown"
     * - 1: "male"
     * - 2: "female"
     */
    type Gender = 0 | 1 | 2;

    /** http method */
    type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

    /** upload file */
    type UploadFile = {
      id: string;
      name: string;
      size: number;
      type: string;
      url: string;
    };

    /** common record */
    type Record<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createdBy: string;
      /** record create time */
      createdAt: string;
      /** record updater */
      updatedBy: string;
      /** record update time */
      updatedAt: string;
      /** record status */
      status: boolean;
    } & T;

    /** common params of paginating query list data */
    interface PaginatingRecord<T = any> {
      records: T[];
      total: number;
    }

    /** common params of paginating */
    interface PaginatingParams {
      /** current page number */
      _page: number;
      /** page size */
      _limit: number;
    }

    /** common search params of table */
    type SearchParams<T = any> = CommonType.RecordNullable<
      T &
        PaginatingParams & {
          _sort?: string;
          _order?: string;
          _embed?: string;
          _expand?: string;
          q?: string;
        }
    >;

    /** common create params of table */
    type CreateParams<T = any> = CommonType.RecordNullable<
      Omit<T, 'id' | 'createBy' | 'createAt' | 'updateBy' | 'updateAt'>
    >;

    /** common update params of table */
    type UpdateParams<T = any> = CommonType.RecordNullable<Omit<T, 'createBy' | 'createAt' | 'updateBy' | 'updateAt'>>;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginParams {
      username: string;
      password: string;
    }

    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      id: string;
      username: string;
      nickname: string;
      gender: Common.Gender;
      avatar: string;
      email: string;
      phone: string;
      roles?: System.Role[];
      buttons?: string[];
    }

    interface UpdateUserInfoParams {
      nickname: string;
      avatar: string;
      gender: Common.Gender;
      email: string;
      phone: string;
    }

    interface UpdatePasswordParams {
      oldpass: string;
      password: string;
      repass: string;
    }

    interface CustomBackendErrorParams {
      code: string;
      message: string;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }
  }

  /**
   * namespace System
   *
   * backend api module: "system"
   */
  namespace System {
    /** config */
    type Config = Common.Record<{
      name: string;
      code: string;
      summary: string;
      params: ConfigParam[];
    }>;

    /** config param */
    type ConfigParam = {
      id: string;
      name: string;
      code: string;
      value: string;
      summary: string;
      status: boolean;
    };

    /** config list */
    type ConfigList = Common.PaginatingRecord<Config>;

    /** config search params */
    type ConfigSearchParams = Common.SearchParams<Pick<Config, 'name' | 'code' | 'status'>>;

    /** config create params */
    type ConfigCreateParams = Common.CreateParams<Config>;

    /** config update params */
    type ConfigUpdateParams = Common.UpdateParams<Config>;

    /** dict */
    type Dict = Common.Record<{
      name: string;
      code: string;
      summary: string;
      options?: DictOption[];
    }>;

    /** dict option */
    type DictOption = {
      id: string;
      label: string;
      value: string;
      type: 'default' | 'success' | 'error' | 'warning' | 'primary' | 'info';
      status: boolean;
    };

    /** dict list */
    type DictList = Common.PaginatingRecord<Dict>;

    /** dict search params */
    type DictSearchParams = Common.SearchParams<Pick<Dict, 'name' | 'code' | 'status'>>;

    /** dict create params */
    type DictCreateParams = Common.CreateParams<Dict>;

    /** dict update params */
    type DictUpdateParams = Common.UpdateParams<Dict>;

    /**
     * menu type
     *
     * - 1: dir
     * - 2: menu
     */
    type MenuType = 1 | 2;

    /** menu layout */
    type MenuLayout = 'default' | 'base' | 'blank';

    /** menu */
    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
      'order' | 'href' | 'constant' | 'hideInMenu' | 'keepAlive' | 'multiTab'
    >;

    /** menu */
    type Menu = Common.Record<{
      menuType: MenuType;
      menuName: string;
      routeName: string;
      routePath: string;
      layout: MenuLayout;
      component: string;
      icon: string;
      parentId: number;
      children?: Menu[];
    }> &
      MenuPropsOfRoute;

    /** menu list */
    type MenuList = Common.PaginatingRecord<Menu>;

    /** menu search params */
    type MenuSearchParams = Common.SearchParams<Pick<Menu, 'menuName' | 'routeName' | 'constant' | 'status'>>;

    /** menu create params */
    type MenuCreateParams = Common.CreateParams<Menu>;

    /** menu update params */
    type MenuUpdateParams = Common.UpdateParams<Menu>;

    /** api */
    type Api = Common.Record<{
      path: string;
      method: Common.HttpMethod;
      tags: string[];
      summary: string;
    }>;

    /** api list */
    type ApiList = Common.PaginatingRecord<Api>;

    /** api search params */
    type ApiSearchParams = Common.SearchParams<Pick<Api, 'path' | 'method' | 'status'>>;

    /** api create params */
    type ApiCreateParams = Common.CreateParams<Api>;

    /** api update params */
    type ApiUpdateParams = Common.UpdateParams<Api>;

    /** dept */
    type Dept = Common.Record<{
      name: string;
      code: string;
      summary: string;
      order: number;
      parentId: number;
    }>;

    /** dept list */
    type DeptList = Common.PaginatingRecord<Dept>;

    /** dept search params */
    type DeptSearchParams = Common.SearchParams<Pick<Dept, 'name' | 'code' | 'status'>>;

    /** dept create params */
    type DeptCreateParams = Common.CreateParams<Dept>;

    /** dept update params */
    type DeptUpdateParams = Common.UpdateParams<Dept>;

    /** role */
    type Role = Common.Record<{
      name: string;
      code: string;
      summary: string;
      home?: string | null;
      menuIds?: number[];
      apiIds?: number[];
    }>;

    /** role list */
    type RoleList = Common.PaginatingRecord<Role>;

    /** role search params */
    type RoleSearchParams = Common.SearchParams<Pick<Role, 'name' | 'code' | 'status'>>;

    /** role create params */
    type RoleCreateParams = Common.CreateParams<Role>;

    /** role update params */
    type RoleUpdateParams = Common.UpdateParams<Role>;

    /** role permit params */
    type RolePermitParams = CommonType.RecordNullable<Pick<Role, 'id' | 'home' | 'menuIds' | 'apiIds'>>;

    /** user */
    type User = Common.Record<{
      username: string;
      password?: string;
      nickname: string;
      gender: Common.Gender;
      phone: string;
      email: string;
      avatar: string;
      roleIds: number[];
      deptId: number;
      roles?: Role[];
      dept?: Dept;
    }>;

    /** user list */
    type UserList = Common.PaginatingRecord<User>;

    /** user search params */
    type UserSearchParams = Common.SearchParams<
      Pick<User, 'username' | 'nickname' | 'gender' | 'phone' | 'email' | 'status' | 'deptId'>
    >;

    /** user create params */
    type UserCreateParams = Common.CreateParams<User>;

    /** user update params */
    type UserUpdateParams = Common.UpdateParams<User>;

    /** user password params */
    type UserPasswordParams = CommonType.RecordNullable<Pick<User, 'id' | 'password'>>;

    /** log */
    type Log = Common.Record<{
      userId: number;
      path: string;
      method: Common.HttpMethod;
      code: number;
      params: object;
      time: number;
      userAgent: string;
      clientIp: string;
      user?: User;
    }>;

    /** log list */
    type LogList = Common.PaginatingRecord<Log>;

    /** log search params */
    type LogSearchParams = Common.SearchParams<Pick<Log, 'userId' | 'path' | 'method' | 'code' | 'clientIp'>>;
  }

  /**
   * namespace Cms
   *
   * backend api module: "cms"
   */
  namespace Cms {
    /** meta */
    type Meta = Common.Record<{
      name: string;
      slug: string;
      cover: string;
      order: number;
      summary: string;
      parentId: number;
    }>;

    /** meta list */
    type MetaList = Common.PaginatingRecord<Meta>;

    /** meta search params */
    type MetaSearchParams = Common.SearchParams<Pick<Meta, 'name' | 'slug' | 'status'>>;

    /** meta create params */
    type MetaCreateParams = Common.CreateParams<Meta>;

    /** meta update params */
    type MetaUpdateParams = Common.UpdateParams<Meta>;

    /** post */
    type Post = Common.Record<{
      metaId: number;
      title: string;
      summary: string;
      content: string;
      slug: string;
      cover: string;
      files: Common.UploadFile[];
      author: string;
      from: string;
      password: string;
      tags: string[];
      order: number;
      flag: number[];
      meta?: Meta;
    }>;

    /** post list */
    type PostList = Common.PaginatingRecord<Post>;

    /** post search params */
    type PostSearchParams = Common.SearchParams<Pick<Post, 'title' | 'slug' | 'flag' | 'metaId' | 'status'>>;

    /** post create params */
    type PostCreateParams = Common.CreateParams<Post>;

    /** post update params */
    type PostUpdateParams = Common.UpdateParams<Post>;
  }

  /**
   * namespace Business
   *
   * backend api module: "business"
   */
  namespace Business {
    /** company */
    type Company = Common.Record<{
      name: string;
      industry: number | null;
      type: number | null;
      size: number | null;
      cert: number[];
      contact: string;
      area: string | null;
      address: string;
      phone: string;
      email: string;
      website: string;
      logo: string;
      tianyanId: string;
      summary: string;
      detail: string;
      albums: Common.UploadFile[];
      tags: string[];
      order: number;
      flag: number[];
    }>;

    /** company list */
    type CompanyList = Common.PaginatingRecord<Company>;

    /** company search params */
    type CompanySearchParams = Common.SearchParams<
      Pick<Company, 'name' | 'industry' | 'type' | 'cert' | 'flag' | 'status'>
    >;

    /** company create params */
    type CompanyCreateParams = Common.CreateParams<Company>;

    /** company update params */
    type CompanyUpdateParams = Common.UpdateParams<Company>;

    /** product */
    type Product = Common.Record<{
      companyId: number | null;
      name: string;
      code: string;
      type: number | null;
      spec: string;
      material: string;
      standard: string;
      surface: string;
      strength: string;
      cover: string;
      detail: string;
      albums: Common.UploadFile[];
      tags: string[];
      order: number;
      flag: number[];
      company?: Company;
    }>;

    /** product list */
    type ProductList = Common.PaginatingRecord<Product>;

    /** product search params */
    type ProductSearchParams = Common.SearchParams<
      Pick<Product, 'companyId' | 'name' | 'code' | 'type' | 'flag' | 'status'>
    >;

    /** company create params */
    type ProductCreateParams = Common.CreateParams<Product>;

    /** product update params */
    type ProductUpdateParams = Common.UpdateParams<Product>;

    /** talent */
    type Talent = Common.Record<{
      name: string;
      gender: number | null;
      age: number | null;
      edu: number | null;
      exp: number | null;
      type: number | null;
      skills: string[];
      area: string | null;
      phone: string;
      email: string;
      avatar: string;
      address: string | null;
      summary: string;
      detail: string;
      party: boolean;
      files: Common.UploadFile[];
      order: number;
      flag: number[];
    }>;

    /** talent list */
    type TalentList = Common.PaginatingRecord<Talent>;

    /** talent search params */
    type TalentSearchParams = Common.SearchParams<
      Pick<Talent, 'name' | 'gender' | 'age' | 'phone' | 'email' | 'address' | 'flag' | 'type' | 'status'>
    >;

    /** talent create params */
    type TalentCreateParams = Common.CreateParams<Talent>;

    /** talent update params */
    type TalentUpdateParams = Common.UpdateParams<Talent>;

    /** expert */
    type Expert = Common.Record<{
      name: string;
      gender: number | null;
      age: number | null;
      edu: number | null;
      exp: number | null;
      type: number[];
      skills: string[];
      area: string;
      phone: string;
      email: string;
      avatar: string;
      tags: string[];
      summary: string;
      detail: string;
      party: boolean;
      order: number;
      flag: number[];
    }>;

    /** expert list */
    type ExpertList = Common.PaginatingRecord<Expert>;

    /** expert search params */
    type ExpertSearchParams = Common.SearchParams<
      Pick<Expert, 'name' | 'gender' | 'age' | 'type' | 'phone' | 'email' | 'party' | 'flag' | 'status'>
    >;

    /** expert create params */
    type ExpertCreateParams = Common.CreateParams<Expert>;

    /** expert update params */
    type ExpertUpdateParams = Common.UpdateParams<Expert>;

    /** knowledge */
    type Knowledge = Common.Record<{
      title: string;
      industry: number | null;
      summary: string;
      content: string;
      tags: string[];
      cover: string;
      files: Common.UploadFile[];
      order: number;
      flag: number[];
    }>;

    /** knowledge list */
    type KnowledgeList = Common.PaginatingRecord<Knowledge>;

    /** knowledge search params */
    type KnowledgeSearchParams = Common.SearchParams<Pick<Knowledge, 'title' | 'industry' | 'flag' | 'status'>>;

    /** knowledge create params */
    type KnowledgeCreateParams = Common.CreateParams<Knowledge>;

    /** knowledge update params */
    type KnowledgeUpdateParams = Common.UpdateParams<Knowledge>;

    /** app */
    type App = Common.Record<{
      name: string;
      slug: string;
      logo: string;
      summary: string;
      url: string;
      tags: string[];
      order: number;
    }>;

    /** app list */
    type AppList = Common.PaginatingRecord<App>;

    /** app search params */
    type AppSearchParams = Common.SearchParams<Pick<App, 'name' | 'slug' | 'status'>>;

    /** app create params */
    type AppCreateParams = Common.CreateParams<App>;

    /** app update params */
    type AppUpdateParams = Common.UpdateParams<App>;

    /** report */
    type Report = Common.Record<{
      companyId: number | null;
      title: string;
      summary: string;
      content: string;
      tags: string[];
      cover: string;
      files: Common.UploadFile[];
      order: number;
      flag: number[];
      company?: Company;
    }>;

    /** report list */
    type ReportList = Common.PaginatingRecord<Report>;

    /** report search params */
    type ReportSearchParams = Common.SearchParams<Pick<Report, 'title' | 'companyId' | 'flag' | 'status'>>;

    /** report create params */
    type ReportCreateParams = Common.CreateParams<Report>;

    /** report update params */
    type ReportUpdateParams = Common.UpdateParams<Report>;

    /** job */
    type Job = Common.Record<{
      companyId: number | null;
      title: string;
      type: number | null;
      salaryType: number | null;
      salary: number[];
      tags: string[];
      detail: string;
      area: string | null;
      contact: string;
      phone: string;
      email: string;
      order: number;
      flag: number[];
      company?: Company;
    }>;

    /** job list */
    type JobList = Common.PaginatingRecord<Job>;

    /** job search params */
    type JobSearchParams = Common.SearchParams<Pick<Job, 'title' | 'companyId' | 'flag' | 'type' | 'status'>>;

    /** job create params */
    type JobCreateParams = Common.CreateParams<Job>;

    /** job update params */
    type JobUpdateParams = Common.UpdateParams<Job>;
  }
}
